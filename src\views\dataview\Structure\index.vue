<script setup>
import { ref, watch, onUnmounted, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { dataview } from '/@/stores/dataview'
import Info from './Left/Info.vue'
import Choose from './Header/Choose.vue'
import Type from './components/Type.vue'
import DeviceList from './Left/DeviceList.vue'
import Chart from './components/Chart.vue'


const store = dataview()
const { currentStructureInfo: data, structureInfoLoading: isLoading, structureCode } = storeToRefs(store)

const deviceCategory = ref('环境类')
const curTab = ref('监测点位')
const selectedDevices = ref([])  // 监测点位的设备选择
const selectedVideoDevices = ref([])  // 监测视频的设备选择
const dataTab = ref('实时数据')

// 当前选中的设备code - 分离为两个独立字段以防相互影响
const currentPointDeviceCode = ref('')  // 监测点位设备code
const currentVideoDeviceCode = ref('')  // 监测视频设备code

// 搜索状态
const monitoringSearchQuery = ref('')  // 监测点位搜索
const videoSearchQuery = ref('')  // 监测视频搜索

// 设备分类映射
import { CATEGORY_KEY_MAP, CATEGORY_NAMES } from './constants/categoryMapping'
const categoryKeyMap = CATEGORY_KEY_MAP

// 获取所有监测点位设备
const allMonitoringDevices = computed(() => {
  if (!data.value || !data.value.deviceList || !deviceCategory.value || deviceCategory.value === '监测视频') {
    return []
  }
  const key = categoryKeyMap[deviceCategory.value]
  return data.value.deviceList[key] || []
})

// 获取所有监测视频设备
const allVideoDevices = computed(() => {
  if (!data.value || deviceCategory.value !== '监测视频') {
    return []
  }
  return data.value.videoList || []
})

// 过滤后的监测点位设备（应用搜索）
const filteredMonitoringDevices = computed(() => {
  if (!monitoringSearchQuery.value) {
    return allMonitoringDevices.value
  }
  return allMonitoringDevices.value.filter(device =>
    device.deviceName.toLowerCase().includes(monitoringSearchQuery.value.toLowerCase())
  )
})

// 过滤后的监测视频设备（应用搜索）
const filteredVideoDevices = computed(() => {
  if (!videoSearchQuery.value) {
    return allVideoDevices.value
  }
  return allVideoDevices.value.filter(device =>
    device.deviceName.toLowerCase().includes(videoSearchQuery.value.toLowerCase())
  )
})

// 自动选择设备的逻辑
const autoSelectDevice = (devices) => {
  if (!devices || devices.length === 0) {
    return []
  }

  // 优先选择有报警的在线设备
  const firstAlarmedDevice = devices.find(d => d.alarmLevel && Number(d.alarmLevel) > 0 && d.status !== '0')
  if (firstAlarmedDevice) {
    return [firstAlarmedDevice]
  }

  // 其次选择在线设备
  const firstOnlineDevice = devices.find(d => d.status !== '0')
  if (firstOnlineDevice) {
    return [firstOnlineDevice]
  }

  // 最后选择第一个设备
  return [devices[0]]
}

// 自动选择视频设备的逻辑（选择前2个）
const autoSelectVideoDevices = (devices) => {
  if (!devices || devices.length === 0) {
    return []
  }
  return devices.slice(0, 2)
}

// 监听监测点位设备变化，自动选择设
watch(
  () => allMonitoringDevices.value,
  (newDevices) => {
    const currentSelectionValid = selectedDevices.value.every(s => newDevices.some(d => d.id === s.id))
    if (currentSelectionValid && selectedDevices.value.length > 0) {
      return
    }
    if ((curTab.value === '监测视频' || deviceCategory.value === '监测视频') && newDevices.length === 0) {
      console.log('监测视频', newDevices);
      return
    }
    const selection = autoSelectDevice(newDevices)
    selectedDevices.value = selection

    // 更新当前监测点位设备code
    if (selection && selection.length > 0 && selection[0].pointUniqueCode) {
      currentPointDeviceCode.value = selection[0].pointUniqueCode
    } else {
      currentPointDeviceCode.value = ''
    }
  },
  { immediate: true, deep: true }
)

// 监听监测视频设备变化，自动选择设备
watch(
  () => allVideoDevices.value,
  (newDevices) => {
    const currentSelectionValid = selectedVideoDevices.value.every(s => newDevices.some(d => d.id === s.id))
    if (currentSelectionValid && selectedVideoDevices.value.length > 0) {
      return
    }

    const selection = autoSelectVideoDevices(newDevices)
    selectedVideoDevices.value = selection

    // 更新当前监测视频设备code
    if (selection && selection.length > 0 && selection[0].deviceSerial) {
      currentVideoDeviceCode.value = selection[0].deviceSerial
    } else {
      currentVideoDeviceCode.value = ''
    }
  },
  { immediate: true, deep: true }
)

// 监听数据变化，清除搜索内容
watch(
  () => data.value,
  () => {
    monitoringSearchQuery.value = ''
    videoSearchQuery.value = ''
  }
)

// 在组件加载时和 structureCode 变化时获取数据
watch(
  structureCode,
  (newCode) => {
    if (newCode) {
      // Clear video list from previous structure to prevent stale display
      if (data.value && data.value.videoList) {
        data.value.videoList = undefined
      }
      selectedDevices.value = [] // Also clear selected devices
      selectedVideoDevices.value = [] // Clear video devices selection
      deviceCategory.value = '' // Reset structure type to fix default selection regression
      currentPointDeviceCode.value = '' // Clear current point device code
      currentVideoDeviceCode.value = '' // Clear current video device code
      monitoringSearchQuery.value = '' // Clear search queries
      videoSearchQuery.value = ''

      // 重置tab到监测点位
      curTab.value = '监测点位'

      store.fetchStructureInfo(newCode)
    }
  },
  { immediate: true }
)

watch(data, (newData) => {
  if (!newData) return;

  const availableStructureTypes = Object.entries(CATEGORY_NAMES).map(([key, name]) => {
    const devices = newData.deviceList?.[key] || [];
    return {
      name,
      count: devices.length,
    };
  });

  const firstTypeWithDevices = availableStructureTypes.find(t => t.count > 0);
  if (firstTypeWithDevices) {
    // 如果有监测点位设备，选择第一个有设备的类型
    deviceCategory.value = firstTypeWithDevices.name;
  } else if (availableStructureTypes.length > 0) {
    deviceCategory.value = availableStructureTypes[0].name;
  } else {
    // 如果没有监测点位设备，检查是否有监测视频
    if (newData.videoList && newData.videoList.length > 0) {
      deviceCategory.value = '监测视频';
    } else {
      deviceCategory.value = '';
    }
  }
}, { deep: true });

// 监听Tab或设备类型变化时，清空已选设备
/* watch([() => curTab.value, () => deviceCategory.value], () => {
  selectedDevices.value = []
}) */

// 处理视频数据加载完成
const handleVideoDataLoaded = (videoData) => {
  if (data.value) {
    data.value.videoList = videoData

    // 检查是否没有监测点位设备，但有监测视频设备
    const hasMonitoringDevices = Object.entries(CATEGORY_NAMES)
      .filter(([key]) => key !== 'video')
      .some(([key]) => {
        const devices = data.value.deviceList?.[key] || []
        return devices.length > 0
      })

    // 如果没有监测点位设备，但有监测视频设备，自动切换到监测视频
    if (!hasMonitoringDevices && videoData && videoData.length > 0) {
      deviceCategory.value = '监测视频'
      // deviceCategory的watch会自动处理tab同步，这里不需要手动设置
    }
  }
}

// 监听Tab切换
watch(
  () => deviceCategory.value,
  (newType, oldType) => {
    console.log(newType, oldType);
    if (newType === '监测视频') {
      curTab.value = '监测视频'
    } else {
      curTab.value = '监测点位'
    }
  }, { immediate: true }
)

// 监听curTab变化，当从监测视频切换回监测点位时重新选择设备分类
watch(
  () => curTab.value,
  (newTab, oldTab) => {
    // 当从监测视频切换到监测点位时，重新应用自动选择设备分类逻辑
    if (oldTab === '监测视频' && (newTab === '监测点位' || newTab === '监测预警') && data.value && deviceCategory.value === '监测视频') {
      const availableStructureTypes = Object.entries(CATEGORY_NAMES).map(([key, name]) => {
        const devices = data.value.deviceList?.[key] || [];
        return {
          name,
          count: devices.length,
        };
      });

      const firstTypeWithDevices = availableStructureTypes.find(t => t.count > 0);
      if (firstTypeWithDevices) {
        deviceCategory.value = firstTypeWithDevices.name;
      } else if (availableStructureTypes.length > 0) {
        deviceCategory.value = availableStructureTypes[0].name;
      } else {
        deviceCategory.value = '';
      }
    }
  }
)

// 监听监测点位设备选择变化，更新当前设备code
watch(
  () => selectedDevices.value,
  (devices) => {
    // 更新当前监测点位设备code
    if (devices && devices.length > 0 && devices[0].pointUniqueCode) {
      currentPointDeviceCode.value = devices[0].pointUniqueCode
    } else {
      currentPointDeviceCode.value = ''
    }
  },
  { deep: true }
)

// 监听监测视频设备选择变化，更新当前设备code
watch(
  () => selectedVideoDevices.value,
  (devices) => {
    // 更新当前监测视频设备code
    if (devices && devices.length > 0 && devices[0].deviceSerial) {
      currentVideoDeviceCode.value = devices[0].deviceSerial
    } else {
      currentVideoDeviceCode.value = ''
    }
  },
  { deep: true }
)

// 组件卸载时清理
onUnmounted(() => {
  // 清理逻辑已移到Chart组件内部
})
</script>

<template>
  <div class="w-full h-full pb-10">
    <Choose />
    <div class="w-full h-full flex gap-2">
      <div class="w-1/4 h-full gap-2 flex flex-col items-center">
        <Info :structure-info="data" :is-loading="isLoading" class="flex-shrink-0" />
        <!-- 监测点位设备列表 -->
        <DeviceList v-show="curTab === '监测点位' || curTab === '监测预警'" :devices="filteredMonitoringDevices" v-model:selectedDevices="selectedDevices" v-model:searchQuery="monitoringSearchQuery" class="w-full flex-grow min-h-0" />
        <!-- 监测视频设备列表 -->
        <DeviceList v-show="curTab === '监测视频'" :devices="filteredVideoDevices" :maxSelectCount="2" v-model:selectedDevices="selectedVideoDevices" v-model:searchQuery="videoSearchQuery" class="w-full flex-grow min-h-0" />
        <div class="w-full h-[286px] p-2 border border-[#0E3067] bg-[#031538] flex flex-col flex-shrink-0">
          <header class="flex justify-between items-center mb-3 flex-shrink-0">
            <div class="flex items-center gap-2">
              <span class="border-l-2 border-blue-500 pl-2 text-base">监测数据</span>
            </div>
            <div>
              <el-radio-group v-model="dataTab" size="small" class="dataview-tabs">
                <el-radio-button label="实时数据" />
                <el-radio-button label="特征值数据" />
              </el-radio-group>
            </div>
          </header>
          <div class="flex-grow min-h-0">
            <Chart :device-code="currentPointDeviceCode" :structure-type="data?.structureType || 0" :structure-id="structureCode" :data-tab="dataTab" />
          </div>
        </div>
      </div>
      <div class="w-3/4 h-full border border-[#0E3067] bg-[#031538] flex flex-col">
        <Type v-if="!isLoading" :selectedDevices="selectedDevices" :selectedVideoDevices="selectedVideoDevices" :data="data" v-model="deviceCategory" @update:tabs="curTab = $event" @video-data-loaded="handleVideoDataLoaded" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.dataview-tabs :deep(.el-radio-button__inner) {
  background-color: transparent;
  color: white;
  border: 1px solid #456591;
  border-left: 0;
}

.dataview-tabs :deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-left: 1px solid #456591;
}

.dataview-tabs :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #258eff;
  border-color: #258eff;
  color: white;
  box-shadow: -1px 0 0 0 #258eff;
}

.dataview-tabs :deep(.el-radio-button__inner:hover) {
  color: #258eff;
}
</style>
