<template>
  <div class="w-full h-full flex flex-col relative" v-loading="isLoading">
    <div class="flex-1 bg-black w-full h-full">
      <LivePlayer v-if="videoUrl" :videoUrl="videoUrl" waterMark="" class="w-full h-full" ref="livePlayer" :timeout="5" hideBigPlayButton dblclickFullscreen :controls="false" hlsTestMethod="GET" />
      <div v-else>视频无法播放</div>
    </div>
    <div class="absolute top-2 right-2 text-white bg-black bg-opacity-50 p-1 rounded">
      {{ label }}
    </div>
    <div class="absolute bottom-2 left-2">
      <el-button @click="emit('zoom', props.deviceSerial)" type="primary" circle>
        <el-icon>
          <Rank v-if="props.isZoomed" />
          <FullScreen v-else />
        </el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { getHikVideoUrl } from '/@/api/dataview/structure.ts';
import LivePlayer from '@liveqing/liveplayer-v3';
import { FullScreen, Rank } from '@element-plus/icons-vue';
import { storeToRefs } from 'pinia';
import { dataview } from '/@/stores/dataview';
import { VideoListSelf } from '../const'

const store = dataview();
const { structureCode: structureUniqueCode } = storeToRefs(store);

const props = defineProps({
  deviceSerial: {
    type: String,
    required: true,
  },
  type: {
    type: Number, required: true
  },
  label: {
    type: String,
  },
  isZoomed: {
    type: Boolean,


    default: false,
  },
});

const emit = defineEmits(['zoom']);

const isLoading = ref(false);
const livePlayer = ref(null);
const videoUrl = ref('');

async function fetchVideoUrlAndPlay() {
  if (!props.deviceSerial) { return; }
  isLoading.value = true;

  try {
    let url;
    if (VideoListSelf[structureUniqueCode.value]) {
      // **关键改动：在这里使用 await 等待 fetch 请求和 JSON 解析完成**
      const response = await fetch(`http://jtjcshm.versoon.cn:18081/api/play/v2/start?cid=${props.deviceSerial}&isSubStream=false`, {
        headers: { 'st': '********************************' }
      });

      // 检查响应是否成功
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const res = await response.json();
      url = res.data.https_flv;

    } else {
      // 这里已经使用了 await，是正确的
      const res = await getHikVideoUrl(props.deviceSerial, structureUniqueCode.value, props.type);
      url = res.data
    }

    if (!url) {
      // 可以在这里根据情况抛出错误或设置特定的错误信息
      // throw new Error('Video URL could not be obtained.');
      return; // 返回，不再执行后续代码
    }

    // 设置视频URL，LivePlayer会自动处理不同格式的视频流
    videoUrl.value = url;

  } catch (error) {
    console.error('Exception while fetching video URL:', error);
    // 可以在这里设置错误状态，例如 error.value = error.message;
  } finally {
    // 无论成功或失败，最后都要将 loading 状态设为 false
    isLoading.value = false;
  }
}
onMounted(fetchVideoUrlAndPlay);
onActivated(() => { fetchVideoUrlAndPlay(); })
onDeactivated(() => { videoUrl.value = '' })
watch(() => props.deviceSerial, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) { fetchVideoUrlAndPlay(); }
});

</script>

<style scoped>
#player {
  background-color: #000;
  /* 占位符颜色 */
  width: 100%;
  /* 默认为容器的全宽 */
  aspect-ratio: 16 / 9;
  /* 保持16:9宽高比 */
  max-width: 800px;
  /* 可选：较大屏幕的最大宽度 */
  margin: 0 auto;
  /* 如果使用max-width，则使播放器居中 */
}

/* 隐藏原生视频控件（如果出现） */
:deep(::-webkit-media-controls) {
  display: none !important;
}
</style>
