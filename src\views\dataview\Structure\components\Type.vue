<template>
  <div class="flex flex-col h-full">
    <header class="w-full h-36">
      <DeviceStatusCards :data="props.data" />
    </header>
    <div class="flex flex-1 gap-2 p-2 overflow-hidden">
      <aside class="w-[130px]">
        <el-radio-group v-model="deviceCategory" size="large" class="flex flex-col items-start custom-radio-group1 gap-5" @change="handleCategoryChange">
          <el-radio-button :key="type.name" v-for="type in availableStructureTypes" :label="type.name">
            <div class="flex justify-between items-center w-full gap-2">
              <span>{{ type.name }}</span>
              <span class="text-sm text-orange-500">{{ type.count }}</span>
            </div>
          </el-radio-button>
        </el-radio-group>
      </aside>
      <main class="flex-1 h-full p-2 border border-[#0E3067] bg-[#031538] overflow-auto">
        <div v-if="curTabs === '监测点位'" class="w-full h-full flex flex-col">
          <div class="flex-1 w-full relative overflow-hidden border border-[#0E3872] bg-white">
            <template v-if="currentImages.length > 0 && currentImages[selectedImageIndex]">
              <div class="image-container">
                <img :src="currentImages[selectedImageIndex].fileUrl" :alt="currentImages[selectedImageIndex].fileName" />
              </div>
            </template>
            <template v-else>
              <div class="w-full h-full flex flex-col items-center justify-center text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span class="text-xl">暂无图片</span>
              </div>
            </template>
          </div>
          <div v-if="currentImages.length > 1" class="h-24 p-2 flex justify-start items-center gap-2 overflow-x-auto">
            <div v-for="(image, index) in currentImages" :key="image.fileUrl" @click="selectedImageIndex = index" class="w-28 h-full cursor-pointer p-1 rounded flex-shrink-0" :class="index === selectedImageIndex ? 'bg-blue-500' : 'bg-gray-700/50'">
              <img :src="image.fileUrl" :alt="image.fileName" class="w-full h-full object-cover bg-white" />
            </div>
          </div>
        </div>
        <!-- 监测视频视图 - 使用 v-show 保持状态 -->
        <div v-show="curTabs === '监测视频'" class="w-full h-full">
          <VideoGrid :selectedDevices="props.selectedVideoDevices" />
        </div>
        <!-- 监测预警视图 -->
        <div v-show="curTabs === '监测预警'" class="w-full h-full">
          <MonitorWarning :device-code="props.selectedDevices?.[0]?.pointUniqueCode || ''" :structure-type="props.data?.structureType || 0" :structure-id="props.data?.structureCode || null" :selected-devices="props.selectedDevices" />
        </div>
      </main>
    </div>
    <footer class="w-full h-14 flex justify-center items-center">
      <el-radio-group v-model="curTabs" size="large" class="custom-radio-group" @change="handleTabChange">
        <el-radio-button label="监测点位">
          <div class="flex justify-center items-center gap-2">
            <el-icon>
              <img src="../icon/position.svg" />
            </el-icon>
            <span>监测点位</span>
          </div>
        </el-radio-button>
        <el-radio-button label="监测视频">
          <div class="flex justify-center items-center gap-2">
            <el-icon>
              <img src="../icon/video.svg" alt="" />
            </el-icon>
            <span>监测视频</span>
            <span v-if="videoDeviceCount > 0" class="text-sm text-orange-500">({{ videoDeviceCount }})</span>
          </div>
        </el-radio-button>
        <el-radio-button label="监测预警">
          <div class="flex justify-center items-center gap-2">
            <el-icon>
              <img src="../icon/warning.svg" alt="" />
            </el-icon>
            <span>监测预警</span>
          </div>
        </el-radio-button>
      </el-radio-group>
    </footer>
  </div>
</template>
<script setup>
import { ref, watch, onMounted, computed, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { dataview } from '/@/stores/dataview'
import { getHikVideoList } from '/@/api/dataview/structure'
import DeviceStatusCards from './self/DeviceStatusCards.vue'
import VideoGrid from './VideoGrid.vue'
import MonitorWarning from './MonitorWarning.vue'
import { VideoListSelf } from '../const'
// 获取store中的structureCode
const store = dataview()
const { structureCode } = storeToRefs(store)

// Props
const props = defineProps({
  selectedDevices: {
    type: Array,
    default: () => [],
  },
  selectedVideoDevices: {
    type: Array,
    default: () => [],
  },
  data: {
    type: Object,
    default: () => ({}),
  },
})

// State
const curTabs = ref('监测点位')
const deviceCategory = defineModel({ default: '环境类' })
const selectedImageIndex = ref(0)
const videoList = ref([]) // 本地存储的视频列表
import { CATEGORY_NAMES } from '../constants/categoryMapping'
const categoryNames = CATEGORY_NAMES

const lastSelectedDeviceCategory = ref('')

// Emits
const emit = defineEmits(['update:tabs', 'video-data-loaded'])

// Computed Properties
const availableStructureTypes = computed(() => {
  const deviceList = props.data?.deviceList

  if (!deviceList && videoList.value.length === 0) {
    return []
  }

  return Object.entries(categoryNames)
    .filter(([key]) => {
      if (key === 'video') {
        // 只要有结构数据就显示监测视频选项，不需要等待视频数据加载
        return props.data && Object.keys(props.data).length > 0
      }
      return deviceList && Object.prototype.hasOwnProperty.call(deviceList, key)
    })
    .map(([key, name]) => ({
      name,
      count: key === 'video'
        ? videoList.value.length
        : (Array.isArray(deviceList[key]) ? deviceList[key].length : 0),
    }))
})

const categoryKeyMap = computed(() => {
  return Object.entries(categoryNames).reduce((acc, [key, name]) => {
    acc[name] = key
    return acc
  }, {})
})

const currentImages = computed(() => {
  const key = categoryKeyMap.value[deviceCategory.value]
  if (key && props.data?.imageList) {
    return props.data.imageList[key] || []
  }
  return []
})

// 获取视频设备数量
const videoDeviceCount = computed(() => {
  return videoList.value.length
})

// 加载视频数据
const loadVideoData = async () => {
  if (!structureCode.value) return

  try {
    let res = {};
    if (VideoListSelf[structureCode.value]) {
      res.data = VideoListSelf[structureCode.value]
    } else {
      res = await getHikVideoList(structureCode.value)
    }
    console.log(res);
    if (res.data) {
      const videoData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data
      videoList.value = videoData.map((item) => ({
        id: item.deviceSerial,
        deviceName: item.deviceName,
        deviceType: item.type,
        deviceSerial: item.deviceSerial,
        status: 1,
      }))
    } else {
      videoList.value = []
    }
    // 通知父组件视频数据已加载
    emit('video-data-loaded', videoList.value)
  } catch (e) {
    console.error('加载视频数据失败:', e)
    videoList.value = []
    emit('video-data-loaded', [])
  }
}

// 避免循环更新的标志
const isUpdating = ref(false)

// 状态同步函数
const syncTabsAndCategory = (newTab, newCategory) => {
  if (isUpdating.value) return
  isUpdating.value = true

  if (newTab) {
    curTabs.value = newTab
  }
  if (newCategory) {
    deviceCategory.value = newCategory
  }

  emit('update:tabs', curTabs.value)
  selectedImageIndex.value = 0

  nextTick(() => {
    isUpdating.value = false
  })
}

// 处理标签页切换
const handleTabChange = (newTab) => {
  const oldTab = curTabs.value

  if (newTab === '监测视频') {
    // 记住当前的设备类别（如果不是监测视频的话）
    if (deviceCategory.value !== '监测视频' && availableStructureTypes.value.some(t => t.name === deviceCategory.value)) {
      lastSelectedDeviceCategory.value = deviceCategory.value
    }
    syncTabsAndCategory(newTab, '监测视频')
  } else if (newTab === '监测点位' && oldTab === '监测视频') {
    // 从监测视频切回监测点位，恢复之前选择的设备类别
    const targetCategory = lastSelectedDeviceCategory.value &&
      availableStructureTypes.value.some(t => t.name === lastSelectedDeviceCategory.value)
      ? lastSelectedDeviceCategory.value
      : availableStructureTypes.value.find(t => t.name !== '监测视频')?.name || '环境类'
    syncTabsAndCategory(newTab, targetCategory)
  } else {
    // 其他情况（如监测预警），只切换标签页，不改变设备类别
    syncTabsAndCategory(newTab, null)
  }
}

// 处理设备类别切换
const handleCategoryChange = (newCategory) => {
  if (newCategory === '监测视频') {
    // 点击监测视频类别，切换到监测视频标签页
    syncTabsAndCategory('监测视频', newCategory)
  } else {
    // 点击其他设备类别
    lastSelectedDeviceCategory.value = newCategory
    // 如果当前在监测视频标签页，切换到监测点位
    const targetTab = curTabs.value === '监测视频' ? '监测点位' : curTabs.value
    syncTabsAndCategory(targetTab, newCategory)
  }
}

// 监听structureCode变化，加载视频数据
watch(
  structureCode,
  (newCode) => {
    if (newCode) {
      loadVideoData()
    } else {
      videoList.value = []
    }
  },
  { immediate: true }
)

// 初始化设备类型选择
const initializeDeviceCategory = () => {
  // debugger
  // 检查是否所有类型都没有设备
  const hasAnyDevices = availableStructureTypes.value.some(t => t.count > 0)

  let initialType
  if (hasAnyDevices) {
    // 如果有设备，选择第一个有设备的类型
    initialType = availableStructureTypes.value.find(t => t.count > 0)?.name || availableStructureTypes.value[0]?.name || ''
  } else {
    // 如果所有类型都没有设备，选择第一个类型
    initialType = availableStructureTypes.value[0]?.name || ''
  }
  console.log(initialType, '谁是初始化');

  // 如果初始类型不是监测视频，则记录为最后选择的设备类别
  if (initialType !== '监测视频') {
    lastSelectedDeviceCategory.value = initialType
  }

  if (!deviceCategory.value || !availableStructureTypes.value.find(t => t.name === deviceCategory.value)) {
    deviceCategory.value = initialType
    console.log(deviceCategory.value, initialType, availableStructureTypes.value);
  }
}

// 监听availableStructureTypes变化，在视频数据加载完成后初始化设备类型
watch(
  availableStructureTypes,
  () => {
    initializeDeviceCategory()
  },
  { immediate: true }
)

// 监听deviceCategory变化，确保与tabs同步
watch(
  () => deviceCategory.value,
  (newCategory) => {
    if (isUpdating.value) return

    if (newCategory === '监测视频') {
      // 当设备类别切换到监测视频时，确保tab也切换到监测视频
      if (curTabs.value !== '监测视频') {
        syncTabsAndCategory('监测视频', '监测视频')
      }
    } else if (curTabs.value === '监测视频' && newCategory !== '监测视频') {
      // 当设备类别从监测视频切换到其他类别时，切换tab到监测点位
      syncTabsAndCategory('监测点位', newCategory)
    }
  },
  { immediate: true }
)

// Lifecycle Hooks
onMounted(() => {
  emit('update:tabs', curTabs.value)
})
</script>
<style scoped>
.custom-radio-group :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #258EFF;
  border-color: #258EFF;
  color: #fff;
  box-shadow: -1px 0 0 0 #258EFF;
}

.custom-radio-group :deep(.el-radio-button__inner) {
  color: #fFF;
  background-color: rgba(29, 155, 240, 0);
  border-color: #2E76C3;
}

.custom-radio-group1 :deep(.el-radio-button__inner) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fFF;
  background-color: #082553;
  width: 100%;
  border-color: #2E76C3;
  border: 0px solid;
  border-radius: 1px;
}

.custom-radio-group1 :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #258EFF;
  border-color: #258EFF;
  color: #fff;
  box-shadow: -1px 0 0 0 #258EFF;
}

.custom-radio-group1 :deep(.el-radio-button) {
  width: 100%;
}

/* 图片最大化显示优化 */
.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}
</style>